# main.py
from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from transformers import pipeline
from fastapi.middleware.cors import CORSMiddleware


# Initialize the FastAPI app
app = FastAPI()

# Load the model pipeline once when the application starts
# Using a smaller model for a quick example. Replace with your chosen model.
generator = pipeline("text-generation", model="distilgpt2") 

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define the request body structure
class Prompt(BaseModel):
    text: str

# Define the API endpoint
@app.post("/generate")
def generate_text(prompt: Prompt):
    # Use the loaded model to generate a response
    input_text = prompt.text
    result = generator(input_text, max_length=50, num_return_sequences=1)
    
    # Return the generated text
    return {"response": result[0]['generated_text']}

# A simple root endpoint to check if the API is running
@app.get("/")
def read_root():
    return {"status": "Model API is running"}