<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Text Generator</title>
</head>
<body>
    <h1>Text Generation</h1>
    <textarea id="prompt" rows="5" cols="50" placeholder="Enter your prompt here..."></textarea><br><br>
    <button onclick="generateText()">Generate</button>
    <h3>Generated Text:</h3>
    <pre id="output"></pre>

    <script>
        async function generateText() {
            const promptText = document.getElementById("prompt").value;

            const response = await fetch("http://127.0.0.1:8000/generate", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ text: promptText })
            });

            const data = await response.json();
            document.getElementById("output").textContent = data.response;
        }
    </script>
</body>
</html>
